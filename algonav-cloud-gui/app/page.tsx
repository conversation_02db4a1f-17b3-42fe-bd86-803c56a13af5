"use client"

import React, { useState, useEffect, useMemo } from 'react';
import { 
  Box, 
  Grid, 
  Paper, 
  Card, 
  CardContent, 
  Divider,
  Badge,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  Button
} from '@mui/material';
import { 
  Cloud as CloudIcon,
  TaskAlt as TaskAltIcon,
  DynamicFeed as DynamicFeedIcon,
  Storage as StorageIcon,
  AccessTime as AccessTimeIcon,
  Folder as FolderIcon,
  ChevronRight as ChevronRightIcon,
  MoreHoriz as MoreHorizIcon,
  Pending as PendingIcon,
  Error as ErrorIcon,
  Description as DescriptionIcon,
  Dataset as DatasetIcon,
  CloudDone as CloudDoneIcon
} from '@mui/icons-material';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart } from '@mui/x-charts';
import { pieArcClasses } from '@mui/x-charts/PieChart';
import type { DefaultizedPieValueType } from '@mui/x-charts/models';
import type { PieItemIdentifier } from '@mui/x-charts/models';
import Link from 'next/link';
import { PageContainer } from '@/components/layout/PageContainer';
import { ErrorDialog } from '@/components/dialogs/ErrorDialog';
import { useDatasets } from '@/lib/hooks/useDatasets';
import { useTemplates } from '@/lib/hooks/useTemplates';
import { formatDate, getStatusDisplay } from '@/utils/jobUtils';
import { useTheme } from '@mui/material/styles';
import Dynamic from 'next/dynamic';

// Status indicator component
interface StatusIndicatorProps {
  status?: 'online' | 'warning' | 'offline' | 'normal';
  label: string;
  value: string | number;
}

const StatusIndicator: React.FC<StatusIndicatorProps> = ({ status = 'online', label, value }) => {
  const statusColors = {
    online: 'success',
    warning: 'warning',
    offline: 'error',
    normal: 'success'
  } as const;
  
  return (
    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
      <Badge
        variant="dot"
        sx={{ mr: 1 }}
        color={statusColors[status]}
      />
      <Box component="span" sx={{ color: 'text.secondary', typography: 'body2' }}>
        {label}:
      </Box>
      <Box component="span" sx={{ ml: 0.5, typography: 'body2', fontWeight: 'bold' }}>
        {value}
      </Box>
    </Box>
  );
};

// Custom card component
interface DashboardCardProps {
  title: string;
  children: React.ReactNode;
  icon: React.ElementType;
  action?: React.ReactNode;
  height?: string | number;
}

const DashboardCard: React.FC<DashboardCardProps> = ({ title, children, icon, action, height = 'auto' }) => {
  const IconComponent = icon;
  
  return (
    <Card elevation={0} sx={{ height, display: 'flex', flexDirection: 'column' }}>
      <CardContent sx={{ p: 2, flex: 1 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {icon && <IconComponent color="primary" sx={{ mr: 1 }} />}
            <Box component="h2" sx={{ typography: 'h6', m: 0 }}>{title}</Box>
          </Box>
          {action}
        </Box>
        <Divider sx={{ my: 1 }} />
        {children}
      </CardContent>
    </Card>
  );
};

interface MockJob {
  id: number;
  name: string;
  template: string;
  status: string;
  dataset: string;
  created_at: string;
}

interface MockDataset {
  id: number;
  name: string;
  files: number;
  size: string;
  created_at: string;
}

export default function Home() {
    const { data: datasetsData, error: datasetsError } = useDatasets();
    const { data: templatesData, error: templatesError } = useTemplates();
    const [error, setError] = useState<string | null>(null);
    const theme = useTheme();

    // Mock data for the dashboard
    const mockJobs = useMemo<MockJob[]>(() => [
        { id: 8347, name: "Large Dataset Processing", template: "3D Point Cloud Analysis", status: "completed", dataset: "City_3D_Model_Berlin", created_at: "2025-03-20T14:30:00Z" },
        { id: 8346, name: "Highway Navigation Processing", template: "Positioning Algorithm", status: "running", dataset: "Highway_A9_Scan_03", created_at: "2025-03-21T08:15:00Z" },
        { id: 8345, name: "Urban Street Processing", template: "Street Detection", status: "pending", dataset: "Munich_Downtown_Survey", created_at: "2025-03-21T06:45:00Z" },
        { id: 8344, name: "Test Processing", template: "Basic Positioning", status: "failed", dataset: "Test_Dataset_Small", created_at: "2025-03-20T19:22:00Z" },
        { id: 8343, name: "Rural Road Analysis", template: "Road Edge Detection", status: "completed", dataset: "Rural_Road_Dataset_B2", created_at: "2025-03-20T10:00:00Z" }
    ], []);

    // Mock datasets
    const mockDatasets = useMemo<MockDataset[]>(() => [
        { id: 1, name: "City_3D_Model_Berlin", files: 128, size: "3.2 GB", created_at: "2025-03-15T10:30:00Z" },
        { id: 2, name: "Highway_A9_Scan_03", files: 64, size: "1.8 GB", created_at: "2025-03-18T14:15:00Z" },
        { id: 3, name: "Munich_Downtown_Survey", files: 96, size: "2.5 GB", created_at: "2025-03-19T09:20:00Z" },
        { id: 4, name: "Test_Dataset_Small", files: 12, size: "450 MB", created_at: "2025-03-20T16:45:00Z" }
    ], []);

    // Mock data for weekly jobs
    const weeklyJobsData = [25, 30, 15, 20, 35, 15, 13];

    // Job status data for pie chart
    const jobStatusData = [
        { id: 0, value: 65, label: 'Completed', color: theme.palette.success.main },
        { id: 1, value: 15, label: 'Running', color: theme.palette.info.main },
        { id: 2, value: 12, label: 'Pending', color: theme.palette.warning.main },
        { id: 3, value: 8, label: 'Failed', color: theme.palette.error.main },
    ];

    // Status color mapping
    const getStatusColor = (status: string): 'success' | 'info' | 'warning' | 'error' | undefined => {
        switch(status.toLowerCase()) {
            case 'completed': return 'success';
            case 'running': return 'info';
            case 'pending': return 'warning';
            case 'failed': return 'error';
            default: return undefined;
        }
    };

    // Status icon mapping
    const getStatusIcon = (status: string): React.ReactNode => {
        switch(status.toLowerCase()) {
            case 'completed': return <TaskAltIcon />;
            case 'running': return <CloudDoneIcon />;
            case 'pending': return <PendingIcon />;
            case 'failed': return <ErrorIcon />;
            default: return <MoreHorizIcon />;
        }
    };

    useEffect(() => {
        if (datasetsError) {
            setError(datasetsError.message || 'Failed to load datasets');
        }
        if (templatesError) {
            setError(templatesError.message || 'Failed to load templates');
        }
    }, [datasetsError, templatesError]);

    return (
        <PageContainer>
            <Box sx={{ px: 2, pt: 2 }}>
                <Box component="h1" sx={{ typography: 'h4', mb: 3, fontWeight: 'bold' }}>
                    Dashboard
                </Box>

                <Grid container spacing={3}>
                    {/* Cloud Status Card */}
                    <Grid item xs={12} md={4}>
                        <Paper 
                            elevation={0} 
                            sx={{ 
                                p: 2, 
                                borderRadius: 2, 
                                border: `1px solid ${theme.palette.divider}`,
                                bgcolor: 'rgba(25, 118, 210, 0.04)',
                                height: '100%'
                            }}
                        >
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                <CloudIcon color="primary" sx={{ fontSize: 28, mr: 1 }} />
                                <Box component="h2" sx={{ typography: 'h6', m: 0, fontWeight: 'bold' }}>Cloud Status</Box>
                            </Box>
                            
                            <StatusIndicator status="online" label="Positioning Cloud" value="Connected" />
                            <StatusIndicator status="online" label="Worker Nodes" value="512 Connected" />
                            <StatusIndicator status="normal" label="System Load" value="23%" />
                            
                            <Box sx={{ mt: 2, mb: 1 }}>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                    <Box component="span" sx={{ typography: 'body2' }}>Storage Usage</Box>
                                    <Box component="span" sx={{ typography: 'body2', fontWeight: 'medium' }}>3 TB / 100 TB</Box>
                                </Box>
                                <LinearProgress 
                                    variant="determinate" 
                                    value={3} 
                                    sx={{ mt: 1, height: 8, borderRadius: 4 }} 
                                />
                            </Box>
                            
                            <Box sx={{ mt: 2 }}>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                    <Box component="span" sx={{ typography: 'body2' }}>Current Job Capacity</Box>
                                    <Box component="span" sx={{ typography: 'body2', fontWeight: 'medium' }}>3 / 5000 Jobs</Box>
                                </Box>
                                <LinearProgress 
                                    variant="determinate" 
                                    value={0.06}  
                                    sx={{ mt: 1, height: 8, borderRadius: 4 }} 
                                />
                            </Box>
                        </Paper>
                    </Grid>

                    {/* Job Status Chart */}
                    <Grid item xs={12} md={4}>
                        <Paper 
                            elevation={0} 
                            sx={{ 
                                p: 2, 
                                borderRadius: 2, 
                                border: `1px solid ${theme.palette.divider}`,
                                height: '100%',
                                display: 'flex',
                                flexDirection: 'column'
                            }}
                        >
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                <TaskAltIcon color="primary" sx={{ fontSize: 28, mr: 1 }} />
                                <Box component="h2" sx={{ typography: 'h6', m: 0, fontWeight: 'bold' }}>Job Status</Box>
                            </Box>
                            
                            <Box sx={{ 
                                display: 'flex', 
                                justifyContent: 'center',
                                alignItems: 'center',
                                flexDirection: 'column',
                                width: '100%',
                                mt: 2,
                                height: '200px'
                            }}>
                                <Box sx={{ width: '100%', maxWidth: 400, height: 160 }}>
                                    <PieChart
                                        series={[
                                            {
                                                data: jobStatusData,
                                                innerRadius: 30,
                                                outerRadius: 80,
                                                paddingAngle: 2,
                                                cornerRadius: 4,
                                                highlightScope: { faded: 'global', highlighted: 'item' },
                                                valueFormatter: (value: any) => `${value.value} jobs`
                                            },
                                        ]}
                                        height={160}
                                        width={400}
                                        slotProps={{
                                            legend: {
                                                hidden: true
                                            }
                                        }}
                                        tooltip={{
                                            trigger: 'item'
                                        }}
                                        sx={{
                                            [`.${pieArcClasses.root}:hover`]: {
                                                filter: 'brightness(0.9)'
                                            },
                                            '.MuiChartsLegend-mark': {
                                                rx: 4,
                                                ry: 4
                                            }
                                        }}
                                    />
                                </Box>
                            </Box>
                            
                            <Box sx={{ 
                                display: 'grid',
                                gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',
                                gap: 1,
                                mt: 2,
                                px: 2,
                                width: '100%',
                                maxWidth: 400,
                                justifyItems: 'center'
                            }}>
                                {jobStatusData.map((status) => (
                                    <Box 
                                        key={status.id} 
                                        sx={{ 
                                            display: 'flex', 
                                            alignItems: 'center',
                                            minWidth: 'fit-content'
                                        }}
                                    >
                                        <Box
                                            sx={{
                                                width: 8,
                                                height: 8,
                                                borderRadius: '50%',
                                                bgcolor: status.color,
                                                mr: 1,
                                                flexShrink: 0,
                                                boxShadow: (theme) => `0 0 0 2px ${theme.palette.background.paper}`,
                                                border: (theme) => `1px solid ${theme.palette.divider}`
                                            }}
                                        />
                                        <Box 
                                            component="span" 
                                            sx={{ 
                                                typography: 'caption', 
                                                color: 'text.secondary',
                                                whiteSpace: 'nowrap',
                                                fontWeight: 'medium'
                                            }}
                                        >
                                            {status.label} ({status.value})
                                        </Box>
                                    </Box>
                                ))}
                            </Box>
                        </Paper>
                    </Grid>

                    {/* Weekly Jobs Chart */}
                    <Grid item xs={12} md={4}>
                        <Paper sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>

                                <DynamicFeedIcon color="primary" sx={{ fontSize: 28, mr: 1 }} />
                                <Box component="h2" sx={{ typography: 'h6', m: 0, fontWeight: 'bold' }}>Weekly Jobs</Box>
                            </Box>
                            <Box sx={{ 
                                flex: 1,
                                display: 'flex', 
                                flexDirection: 'column',
                                alignItems: 'center',
                                justifyContent: 'center'
                            }}>
                                <BarChart
                                    series={[
                                        {
                                            data: weeklyJobsData,
                                            color: theme.palette.primary.main,
                                            valueFormatter: (value) => `${value} jobs`,
                                            highlightScope: { faded: 'global', highlighted: 'item' },
                                        }
                                    ]}
                                    xAxis={[{
                                        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                                        scaleType: 'band',
                                        tickLabelStyle: {
                                            color: theme.palette.text.secondary,
                                            fontSize: 12,
                                            fontWeight: 'medium'
                                        }
                                    }]}
                                    yAxis={[{
                                        tickLabelStyle: {
                                            color: theme.palette.text.secondary,
                                            fontSize: 12,
                                            fontWeight: 'medium'
                                        },
                                        min: 0
                                    }]}
                                    height={200}
                                    margin={{ top: 10, bottom: 24, left: 40, right: 10 }}
                                    slotProps={{
                                        legend: {
                                            hidden: true
                                        },
                                        bar: {
                                            style: {
                                                borderRadius: 4
                                            }
                                        }
                                    }}
                                    tooltip={{
                                        trigger: 'item'
                                    }}
                                    sx={{
                                        '.MuiBarElement-root:hover': {
                                            filter: 'brightness(0.9)'
                                        }
                                    }}
                                />
                            </Box>
                            
                            <Box sx={{ 
                                display: 'flex', 
                                justifyContent: 'space-between', 
                                alignItems: 'center', 
                                mt: 2,
                                width: '100%'
                            }}>
                                <Box component="span" sx={{ typography: 'body2', color: 'text.secondary' }}>
                                    Total Jobs This Week
                                </Box>
                                <Box component="span" sx={{ typography: 'body1', fontWeight: 'bold' }}>
                                    {weeklyJobsData.reduce((sum, val) => sum + val, 0)}
                                </Box>
                            </Box>
                        </Paper>
                    </Grid>

                    {/* Recent Jobs */}
                    <Grid item xs={12} md={6}>
                        <DashboardCard 
                            title="Recent Jobs" 
                            icon={CloudDoneIcon}
                            action={
                                <Button 
                                    component={Link} 
                                    href="/jobs" 
                                    size="small" 
                                    variant="text" 
                                    endIcon={<MoreHorizIcon />}
                                >
                                    View All
                                </Button>
                            }
                            height="100%"
                        >
                            <List sx={{ px: 0 }}>
                                {mockJobs.map((job, index) => (
                                    <React.Fragment key={job.id}>
                                        <ListItem
                                            component={Link}
                                            href={`/jobs/${job.id}`}
                                            sx={{
                                                py: 1,
                                                px: 2,
                                                '&:hover': {
                                                    bgcolor: 'action.hover',
                                                }
                                            }}
                                        >
                                            <ListItemAvatar>
                                                <Avatar 
                                                    sx={{ 
                                                        width: 32, 
                                                        height: 32, 
                                                        bgcolor: getStatusColor(job.status) 
                                                          ? theme.palette[getStatusColor(job.status)!].light 
                                                          : theme.palette.grey[300]
                                                    }}
                                                >
                                                    {getStatusIcon(job.status)}
                                                </Avatar>
                                            </ListItemAvatar>
                                            <ListItemText
                                                primaryTypographyProps={{
                                                    component: 'div'
                                                }}
                                                secondaryTypographyProps={{
                                                    component: 'div'
                                                }}
                                                primary={
                                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                        <Box component="span" sx={{ typography: 'body2', fontWeight: 'medium', maxWidth: 200, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                                                            {job.name}
                                                        </Box>
                                                        <Chip
                                                            size="small"
                                                            label={job.status}
                                                            color={getStatusColor(job.status) || 'default'}
                                                            sx={{ ml: 1, height: 20, fontSize: '0.625rem' }}
                                                        />
                                                    </Box>
                                                }
                                                secondary={
                                                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                                                        <Box component="span" sx={{ typography: 'caption', color: 'text.secondary', mr: 2, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                                                            {job.template}
                                                        </Box>
                                                        <Box component="span" sx={{ typography: 'caption', color: 'text.secondary', display: 'flex', alignItems: 'center' }}>
                                                            <AccessTimeIcon sx={{ fontSize: 12, mr: 0.5 }} />
                                                            {formatDate(job.created_at)}
                                                        </Box>
                                                    </Box>
                                                }
                                            />
                                            <ChevronRightIcon color="action" />
                                        </ListItem>
                                        {index < mockJobs.length - 1 && <Divider variant="inset" component="li" />}
                                    </React.Fragment>
                                ))}
                            </List>
                        </DashboardCard>
                    </Grid>

                    {/* Recent Datasets */}
                    <Grid item xs={12} md={6}>
                        <DashboardCard 
                            title="Recent Datasets" 
                            icon={DatasetIcon}
                            action={
                                <Button 
                                    component={Link} 
                                    href="/datasets" 
                                    size="small" 
                                    variant="text" 
                                    endIcon={<MoreHorizIcon />}
                                >
                                    View All
                                </Button>
                            }
                            height="100%"
                        >
                            <List sx={{ px: 0 }}>
                                {mockDatasets.map((dataset, index) => (
                                    <React.Fragment key={dataset.id}>
                                        <ListItem
                                            component={Link}
                                            href={`/datasets/${dataset.id}`}
                                            sx={{
                                                py: 1,
                                                px: 2,
                                                '&:hover': {
                                                    bgcolor: 'action.hover',
                                                }
                                            }}
                                        >
                                            <ListItemAvatar>
                                                <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.light' }}>
                                                    <StorageIcon />
                                                </Avatar>
                                            </ListItemAvatar>
                                            <ListItemText
                                                primaryTypographyProps={{
                                                    component: 'div'
                                                }}
                                                secondaryTypographyProps={{
                                                    component: 'div'
                                                }}
                                                primary={
                                                    <Box component="span" sx={{ typography: 'body2', fontWeight: 'medium', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                                                        {dataset.name}
                                                    </Box>
                                                }
                                                secondary={
                                                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                                                        <Box component="span" sx={{ typography: 'caption', color: 'text.secondary', mr: 2, display: 'flex', alignItems: 'center' }}>
                                                            <FolderIcon sx={{ fontSize: 12, mr: 0.5 }} />
                                                            {dataset.files} files
                                                        </Box>
                                                        <Box component="span" sx={{ typography: 'caption', color: 'text.secondary', mr: 2 }}>
                                                            {dataset.size}
                                                        </Box>
                                                        <Box component="span" sx={{ typography: 'caption', color: 'text.secondary', display: 'flex', alignItems: 'center' }}>
                                                            <AccessTimeIcon sx={{ fontSize: 12, mr: 0.5 }} />
                                                            {formatDate(dataset.created_at)}
                                                        </Box>
                                                    </Box>
                                                }
                                            />
                                            <ChevronRightIcon color="action" />
                                        </ListItem>
                                        {index < mockDatasets.length - 1 && <Divider variant="inset" component="li" />}
                                    </React.Fragment>
                                ))}
                            </List>
                        </DashboardCard>
                    </Grid>
                </Grid>
            </Box>
            
            {error && <ErrorDialog open={!!error} onClose={() => setError(null)} error={error} />}
        </PageContainer>
    );
}
