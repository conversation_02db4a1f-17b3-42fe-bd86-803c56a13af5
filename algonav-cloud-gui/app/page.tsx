"use client"

import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  Grid,
  Paper,
  Card,
  CardContent,
  Divider,
  Badge,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  Button,
  CircularProgress
} from '@mui/material';
import {
  Cloud as CloudIcon,
  TaskAlt as TaskAltIcon,
  Storage as StorageIcon,
  AccessTime as AccessTimeIcon,
  ChevronRight as ChevronRightIcon,
  MoreHoriz as MoreHorizIcon,
  Pending as PendingIcon,
  Error as ErrorIcon,
  Dataset as DatasetIcon,
  CloudDone as CloudDoneIcon
} from '@mui/icons-material';
import Link from 'next/link';
import { PageContainer } from '@/components/layout/PageContainer';
import { ErrorDialog } from '@/components/dialogs/ErrorDialog';
import { useRecentDatasets } from '@/lib/hooks/useDatasets';
import { useRecentJobs } from '@/lib/hooks/useJobs';
import { formatDashboardDate, getStatusDisplay } from '@/utils/jobUtils';
import { useTheme } from '@mui/material/styles';

// Dashboard data interfaces
interface DashboardJob {
  id: string;
  name: string;
  status: string;
  created_at: string;
}

interface DashboardDataset {
  id: string;
  name: string;
  description?: string;
  created_at: string;
}

// Status indicator component
interface StatusIndicatorProps {
  status?: 'online' | 'warning' | 'offline' | 'normal';
  label: string;
  value: string | number;
}

const StatusIndicator: React.FC<StatusIndicatorProps> = ({ status = 'online', label, value }) => {
  const statusColors = {
    online: 'success',
    warning: 'warning',
    offline: 'error',
    normal: 'success'
  } as const;
  
  return (
    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
      <Badge
        variant="dot"
        sx={{ mr: 1 }}
        color={statusColors[status]}
      />
      <Box component="span" sx={{ color: 'text.secondary', typography: 'body2' }}>
        {label}:
      </Box>
      <Box component="span" sx={{ ml: 0.5, typography: 'body2', fontWeight: 'bold' }}>
        {value}
      </Box>
    </Box>
  );
};

// Custom card component
interface DashboardCardProps {
  title: string;
  children: React.ReactNode;
  icon: React.ElementType;
  action?: React.ReactNode;
  height?: string | number;
}

const DashboardCard: React.FC<DashboardCardProps> = ({ title, children, icon, action, height = 'auto' }) => {
  const IconComponent = icon;
  
  return (
    <Card elevation={0} sx={{ height, display: 'flex', flexDirection: 'column' }}>
      <CardContent sx={{ p: 2, flex: 1 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {icon && <IconComponent color="primary" sx={{ mr: 1 }} />}
            <Box component="h2" sx={{ typography: 'h6', m: 0 }}>{title}</Box>
          </Box>
          {action}
        </Box>
        <Divider sx={{ my: 1 }} />
        {children}
      </CardContent>
    </Card>
  );
};

export default function Home() {
    const { data: recentJobsData, error: jobsError, isLoading: jobsLoading } = useRecentJobs(5);
    const { data: recentDatasetsData, error: datasetsError, isLoading: datasetsLoading } = useRecentDatasets(5);
    const [error, setError] = useState<string | null>(null);
    const theme = useTheme();

    // Get real data from hooks with proper typing
    const recentJobs: DashboardJob[] = recentJobsData?.data || [];
    const recentDatasets: DashboardDataset[] = recentDatasetsData?.data || [];

    // Status color mapping
    const getStatusColor = (status: string): 'success' | 'info' | 'warning' | 'error' | undefined => {
        switch(status.toLowerCase()) {
            case 'completed': return 'success';
            case 'running': return 'info';
            case 'pending': return 'warning';
            case 'failed': return 'error';
            default: return undefined;
        }
    };

    // Status icon mapping
    const getStatusIcon = (status: string): React.ReactNode => {
        switch(status.toLowerCase()) {
            case 'completed': return <TaskAltIcon />;
            case 'running': return <CloudDoneIcon />;
            case 'pending': return <PendingIcon />;
            case 'failed': return <ErrorIcon />;
            default: return <MoreHorizIcon />;
        }
    };

    useEffect(() => {
        if (datasetsError) {
            setError(datasetsError.message || 'Failed to load recent datasets');
        }
        if (jobsError) {
            setError(jobsError.message || 'Failed to load recent jobs');
        }
    }, [datasetsError, jobsError]);

    return (
        <PageContainer>
            <Box sx={{ px: 2, pt: 2 }}>
                <Box component="h1" sx={{ typography: 'h4', mb: 3, fontWeight: 'bold' }}>
                    Dashboard
                </Box>

                <Grid container spacing={3}>
                    {/* Cloud Status Card */}
                    <Grid item xs={12} lg={4}>
                        <DashboardCard
                            title="Cloud Status"
                            icon={CloudIcon}
                            height="100%"
                        >
                            <StatusIndicator status="online" label="Positioning Cloud" value="Connected" />
                            <StatusIndicator status="online" label="Worker Nodes" value="512 Connected" />
                            <StatusIndicator status="normal" label="System Load" value="23%" />

                            <Box sx={{ mt: 2, mb: 1 }}>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                    <Box component="span" sx={{ typography: 'body2' }}>Storage Usage</Box>
                                    <Box component="span" sx={{ typography: 'body2', fontWeight: 'medium' }}>3 TB / 100 TB</Box>
                                </Box>
                                <LinearProgress
                                    variant="determinate"
                                    value={3}
                                    sx={{ mt: 1, height: 8, borderRadius: 4 }}
                                />
                            </Box>

                            <Box sx={{ mt: 2 }}>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                    <Box component="span" sx={{ typography: 'body2' }}>Current Job Capacity</Box>
                                    <Box component="span" sx={{ typography: 'body2', fontWeight: 'medium' }}>3 / 5000 Jobs</Box>
                                </Box>
                                <LinearProgress
                                    variant="determinate"
                                    value={0.06}
                                    sx={{ mt: 1, height: 8, borderRadius: 4 }}
                                />
                            </Box>
                        </DashboardCard>
                    </Grid>

                    {/* Recent Jobs */}
                    <Grid item xs={12} lg={4}>
                        <DashboardCard
                            title="Recent Jobs"
                            icon={CloudDoneIcon}
                            action={
                                <Button
                                    component={Link}
                                    href="/jobs"
                                    size="small"
                                    variant="text"
                                    endIcon={<MoreHorizIcon />}
                                >
                                    View All
                                </Button>
                            }
                            height="100%"
                        >
                            {jobsLoading ? (
                                <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                                    <CircularProgress size={24} />
                                </Box>
                            ) : recentJobs.length === 0 ? (
                                <Box sx={{ textAlign: 'center', py: 4, color: 'text.secondary' }}>
                                    No recent jobs found
                                </Box>
                            ) : (
                                <List sx={{ px: 0 }}>
                                    {recentJobs.map((job, index) => (
                                        <React.Fragment key={job.id}>
                                            <ListItem
                                                component={Link}
                                                href={`/jobs/${job.id}`}
                                                sx={{
                                                    py: 1,
                                                    px: 2,
                                                    '&:hover': {
                                                        bgcolor: 'action.hover',
                                                    }
                                                }}
                                            >
                                                <ListItemAvatar>
                                                    <Avatar
                                                        sx={{
                                                            width: 32,
                                                            height: 32,
                                                            bgcolor: getStatusColor(job.status)
                                                              ? theme.palette[getStatusColor(job.status)!].light
                                                              : theme.palette.grey[300]
                                                        }}
                                                    >
                                                        {getStatusIcon(job.status)}
                                                    </Avatar>
                                                </ListItemAvatar>
                                                <ListItemText
                                                    primaryTypographyProps={{
                                                        component: 'div'
                                                    }}
                                                    secondaryTypographyProps={{
                                                        component: 'div'
                                                    }}
                                                    primary={
                                                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                            <Box component="span" sx={{ typography: 'body2', fontWeight: 'medium', maxWidth: 200, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                                                                {job.name}
                                                            </Box>
                                                            <Chip
                                                                size="small"
                                                                label={job.status}
                                                                color={getStatusColor(job.status) || 'default'}
                                                                sx={{ ml: 1, height: 20, fontSize: '0.625rem' }}
                                                            />
                                                        </Box>
                                                    }
                                                    secondary={
                                                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                                                            <Box component="span" sx={{ typography: 'caption', color: 'text.secondary', display: 'flex', alignItems: 'center' }}>
                                                                <AccessTimeIcon sx={{ fontSize: 12, mr: 0.5 }} />
                                                                {formatDashboardDate(job.created_at)}
                                                            </Box>
                                                        </Box>
                                                    }
                                                />
                                                <ChevronRightIcon color="action" />
                                            </ListItem>
                                            {index < recentJobs.length - 1 && <Divider variant="inset" component="li" />}
                                        </React.Fragment>
                                    ))}
                                </List>
                            )}
                        </DashboardCard>
                    </Grid>

                    {/* Recent Datasets */}
                    <Grid item xs={12} lg={4}>
                        <DashboardCard
                            title="Recent Datasets"
                            icon={DatasetIcon}
                            action={
                                <Button
                                    component={Link}
                                    href="/datasets"
                                    size="small"
                                    variant="text"
                                    endIcon={<MoreHorizIcon />}
                                >
                                    View All
                                </Button>
                            }
                            height="100%"
                        >
                            {datasetsLoading ? (
                                <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                                    <CircularProgress size={24} />
                                </Box>
                            ) : recentDatasets.length === 0 ? (
                                <Box sx={{ textAlign: 'center', py: 4, color: 'text.secondary' }}>
                                    No recent datasets found
                                </Box>
                            ) : (
                                <List sx={{ px: 0 }}>
                                    {recentDatasets.map((dataset, index) => (
                                        <React.Fragment key={dataset.id}>
                                            <ListItem
                                                component={Link}
                                                href={`/datasets/${dataset.id}`}
                                                sx={{
                                                    py: 1,
                                                    px: 2,
                                                    '&:hover': {
                                                        bgcolor: 'action.hover',
                                                    }
                                                }}
                                            >
                                                <ListItemAvatar>
                                                    <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.light' }}>
                                                        <StorageIcon />
                                                    </Avatar>
                                                </ListItemAvatar>
                                                <ListItemText
                                                    primaryTypographyProps={{
                                                        component: 'div'
                                                    }}
                                                    secondaryTypographyProps={{
                                                        component: 'div'
                                                    }}
                                                    primary={
                                                        <Box component="span" sx={{ typography: 'body2', fontWeight: 'medium', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                                                            {dataset.name}
                                                        </Box>
                                                    }
                                                    secondary={
                                                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                                                            {dataset.description && (
                                                                <Box component="span" sx={{ typography: 'caption', color: 'text.secondary', mr: 2, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                                                                    {dataset.description}
                                                                </Box>
                                                            )}
                                                            <Box component="span" sx={{ typography: 'caption', color: 'text.secondary', display: 'flex', alignItems: 'center' }}>
                                                                <AccessTimeIcon sx={{ fontSize: 12, mr: 0.5 }} />
                                                                {formatDashboardDate(dataset.created_at)}
                                                            </Box>
                                                        </Box>
                                                    }
                                                />
                                                <ChevronRightIcon color="action" />
                                            </ListItem>
                                            {index < recentDatasets.length - 1 && <Divider variant="inset" component="li" />}
                                        </React.Fragment>
                                    ))}
                                </List>
                            )}
                        </DashboardCard>
                    </Grid>
                </Grid>
            </Box>
            
            {error && <ErrorDialog open={!!error} onClose={() => setError(null)} error={error} />}
        </PageContainer>
    );
}
