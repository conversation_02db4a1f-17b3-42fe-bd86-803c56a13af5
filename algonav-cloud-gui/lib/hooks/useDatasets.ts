import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { api } from '../services/api'

export function useDatasets() {
    return useQuery({
        queryKey: ['datasets'],
        queryFn: api.getDatasets
    })
}

export function useDataset(id: string | null, options = {}) {
    return useQuery({
        queryKey: ['datasets', id],
        queryFn: () => api.getDataset(id!),
        enabled: !!id,
        ...options
    })
}

export function useCreateDataset() {
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: api.createDataset,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['datasets'] })
        }
    })
}

export function useUpdateDataset() {
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: api.updateDataset,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['datasets'] })
        }
    })
}

export function useDeleteDataset() {
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: api.deleteDataset,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['datasets'] })
        }
    })
}

export function useDatasetFiles(datasetId: string | null) {
    return useQuery({
        queryKey: ['dataset-files', datasetId],
        queryFn: () => api.getDatasetFiles(datasetId!),
        enabled: !!datasetId
    })
}
