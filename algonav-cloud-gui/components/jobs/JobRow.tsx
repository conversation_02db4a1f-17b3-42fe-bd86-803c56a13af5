import React, { memo } from 'react';
import {
  Chip,
  Tooltip,
  Stack,
  IconButton,
  CircularProgress,
  Box,
  Avatar,
  Typography,
  Divider
} from '@mui/material';
import {
  Folder as FolderIcon,
  Description as DescriptionIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import { StyledTableCell, StyledTableRow } from '../common/TablePresets';
import DownloadIcon from '../common/DownloadIcon';
import { formatDate, formatJobName, getStatusDisplay, getFileTypeLabel } from '../../utils/jobUtils'; // Renamed formatBatchName
import { JobRowProps } from '../../types/jobs';
import { useRouter } from 'next/navigation';

const JobRow: React.FC<JobRowProps> = ({
  job, // Renamed batch to job
  downloadingFiles,
  onDownloadFileType,
  onDownloadAll
}) => {
  const router = useRouter();

  const uniqueDatasetNames = getUniqueDatasetNames(job.jobs); // Use job.jobs
  const availableFileTypes = getAvailableFileTypes(job); // Pass job (helper uses job.jobs now)
  const hasResults = availableFileTypes.length > 0;
  const status = getStatusDisplay(job.status); // Use job.status

  const navigateToJob = () => {
    router.push(`/jobs/${job.id}`); // Use job.id
  };

  // Get an icon based on template type
  const getTemplateIcon = () => {
    // Add check for jobs array existence and length
    if (!job.jobs || job.jobs.length === 0) { // Use job.jobs
      // Return a default icon if no tasks or template info available
      return <Avatar sx={{ bgcolor: 'action.disabledBackground', width: 28, height: 28 }}><SettingsIcon fontSize="small" /></Avatar>;
    }
    
    const templateName = job.jobs[0]?.global_job_template?.name?.toLowerCase() || ''; // Use job.jobs
    
    if (templateName.includes('navigation')) {
      return <Avatar sx={{ bgcolor: 'primary.light', width: 28, height: 28 }}>N</Avatar>;
    } else if (templateName.includes('mapping')) {
      return <Avatar sx={{ bgcolor: 'secondary.light', width: 28, height: 28 }}>M</Avatar>;
    } else if (templateName.includes('analysis')) {
      return <Avatar sx={{ bgcolor: 'info.light', width: 28, height: 28 }}>A</Avatar>;
    }
    
    return <Avatar sx={{ bgcolor: 'action.disabledBackground', width: 28, height: 28 }}><SettingsIcon fontSize="small" /></Avatar>;
  };

  return (
    <StyledTableRow
      key={job.id}
      sx={{
        transition: 'all 0.2s ease-in-out',
        '&:hover': {
          backgroundColor: 'rgba(0, 0, 0, 0.02)',
          boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)'
        },
        '& td:not(:last-child)': {
          cursor: 'pointer',
        }
      }}
    >
      <StyledTableCell onClick={navigateToJob} sx={{ color: 'primary.main' }}>
        {job.id}
      </StyledTableCell>

      <StyledTableCell onClick={navigateToJob}>
        <Stack direction="row" spacing={1.5} alignItems="center">
          {getTemplateIcon()}
          <Box>
            <Typography variant="body2" fontWeight={500}>
              {formatJobName(job.name)}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {job.jobs?.[0]?.global_job_template?.name || 'N/A'} {/* Use job.jobs */}
            </Typography>
          </Box>
        </Stack>
      </StyledTableCell>

      <StyledTableCell onClick={navigateToJob}>
        {job.jobs?.[0]?.global_job_template?.name || 'N/A'} {/* Use job.jobs */}
      </StyledTableCell>

      <StyledTableCell onClick={navigateToJob}>
        <Tooltip
          title={
            <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
              {uniqueDatasetNames.length > 0 ? (
                uniqueDatasetNames.map((name, index) => (
                  <div key={index}>{name}</div>
                ))
              ) : (
                <div>No datasets</div>
              )}
            </div>
          }
          arrow
        >
          <Stack direction="row" spacing={1} alignItems="center">
            <FolderIcon fontSize="small" color="action" />
            <span>{uniqueDatasetNames.length}</span>
          </Stack>
        </Tooltip>
      </StyledTableCell>

      <StyledTableCell onClick={navigateToJob}>
        <Tooltip title={status.tooltip || ''} arrow>
          <Chip
            label={status.text}
            color={status.color === 'default' ? 'default' :
                  status.color === 'info' ? 'info' :
                  status.color === 'success' ? 'success' :
                  status.color === 'error' ? 'error' :
                  status.color === 'warning' ? 'warning' : 'default'}
            size="small"
            sx={{
              maxWidth: '150px',
              '& .MuiChip-label': {
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis'
              }
            }}
          />
        </Tooltip>
      </StyledTableCell>

      <StyledTableCell onClick={navigateToJob}>
        {/* Use job.created_at */}
        <Tooltip title={`Created on ${new Date(job.created_at).toLocaleString()}`} arrow>
          <Typography variant="body2">{formatDate(job.created_at)}</Typography>
        </Tooltip>
      </StyledTableCell>

      <StyledTableCell align="left">
        {hasResults ? (
          <Stack
            direction="row"
            spacing={1}
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'flex-start',
              minHeight: '32px',
            }}
          >
            {availableFileTypes.map((fileType) => {
              const key = `${job.id}-${fileType}`; // Use job.id
              return (
                <Tooltip
                  key={fileType}
                  title={`Download ${getFileTypeLabel(fileType)}`}
                >
                  <span>
                    <IconButton
                      size="small"
                      onClick={() => onDownloadFileType(job.id, fileType)} // Pass job.id
                      disabled={downloadingFiles[key]}
                      aria-label={`Download ${getFileTypeLabel(fileType)} files`}
                      sx={{
                        padding: '4px',
                        color: 'text.secondary',
                        '&:hover': {
                          color: 'primary.main',
                          backgroundColor: 'rgba(0, 0, 0, 0.04)'
                        }
                      }}
                    >
                      {downloadingFiles[key] ? (
                        <CircularProgress size={16} />
                      ) : (
                        <DownloadIcon fileType={fileType} isMulti={true} />
                      )}
                    </IconButton>
                  </span>
                </Tooltip>
              );
            })}
            {availableFileTypes.length > 1 && (
              <>
                <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />
                <Tooltip title="Download all files">
                  <span>
                    <IconButton
                      size="small"
                      onClick={() => onDownloadAll(job.id)} // Pass job.id
                      disabled={downloadingFiles[`${job.id}-all`]} // Use job.id
                      aria-label="Download all files"
                      sx={{
                        padding: '4px',
                        color: 'text.secondary',
                        '&:hover': {
                          color: 'primary.main',
                          backgroundColor: 'rgba(0, 0, 0, 0.04)'
                        }
                      }}
                    >
                      {downloadingFiles[`${job.id}-all`] ? ( // Use job.id
                        <CircularProgress size={16} />
                      ) : (
                        <DownloadIcon fileType="all" isMulti={true} />
                      )}
                    </IconButton>
                  </span>
                </Tooltip>
              </>
            )}
          </Stack>
        ) : (
          <Typography variant="caption" color="text.secondary">
            No downloads available
          </Typography>
        )}
      </StyledTableCell>
    </StyledTableRow>
  );
};

// Helper functions
const getUniqueDatasetNames = (tasks: any[] | undefined | null): string[] => { // Parameter name 'tasks' is fine here
  if (!tasks) { // Check the passed array (which will be job.jobs)
    return [];
  }
  const uniqueNames = new Set(tasks.map(task => task?.dataset?.name).filter(Boolean));
  return Array.from(uniqueNames) as string[];
};

const getAvailableFileTypes = (job: any) => { // Renamed batch parameter to job
  const fileTypes = new Set();
  if (!job.jobs) { // Use job.jobs
    return [];
  }

  job.jobs.forEach((task: any) => { // Use job.jobs
    if (task.task_results && Array.isArray(task.task_results)) {
      task.task_results
        .filter((result: any) => result.visible)
        .forEach((result: any) => fileTypes.add(result.file_type));
    }
  });

  return Array.from(fileTypes) as string[];
};

export default memo(JobRow);
